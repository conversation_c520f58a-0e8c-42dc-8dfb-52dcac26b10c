// Global Supabase Mock Factory
// This provides a centralized, configurable mock for all Supabase operations

export interface MockSupabaseClient {
  auth: {
    signInWithOtp: jest.Mock;
    verifyOtp: jest.Mock;
    signInWithPassword: jest.Mock;
    signInWithOAuth: jest.Mock;
    signOut: jest.Mock;
    getUser: jest.Mock;
    getSession: jest.Mock;
    onAuthStateChange: jest.Mock;
  };
  from: jest.Mock;
  storage: {
    from: jest.Mock;
  };
  realtime: {
    channel: jest.Mock;
  };
  rpc: jest.Mock;
}

export interface MockQueryBuilder {
  select: jest.Mock;
  insert: jest.Mock;
  update: jest.Mock;
  delete: jest.Mock;
  eq: jest.<PERSON>ck;
  neq: jest.Mock;
  gt: jest.Mock;
  gte: jest.<PERSON>ck;
  lt: jest.Mock;
  lte: jest.Mock;
  like: jest.<PERSON>ck;
  ilike: jest.<PERSON>ck;
  is: jest.<PERSON>ck;
  in: jest.Mock;
  contains: jest.<PERSON>ck;
  containedBy: jest.Mock;
  rangeGt: jest.Mock;
  rangeGte: jest.Mock;
  rangeLt: jest.Mock;
  rangeLte: jest.<PERSON>ck;
  rangeAdjacent: jest.Mock;
  overlaps: jest.Mock;
  textSearch: jest.Mock;
  match: jest.Mock;
  not: jest.Mock;
  or: jest.Mock;
  filter: jest.Mock;
  order: jest.Mock;
  limit: jest.Mock;
  range: jest.Mock;
  abortSignal: jest.Mock;
  single: jest.Mock;
  maybeSingle: jest.Mock;
  csv: jest.Mock;
  geojson: jest.Mock;
  explain: jest.Mock;
  rollback: jest.Mock;
  returns: jest.Mock;
}

export function createMockQueryBuilder(): MockQueryBuilder {
  const queryBuilder: MockQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    containedBy: jest.fn().mockReturnThis(),
    rangeGt: jest.fn().mockReturnThis(),
    rangeGte: jest.fn().mockReturnThis(),
    rangeLt: jest.fn().mockReturnThis(),
    rangeLte: jest.fn().mockReturnThis(),
    rangeAdjacent: jest.fn().mockReturnThis(),
    overlaps: jest.fn().mockReturnThis(),
    textSearch: jest.fn().mockReturnThis(),
    match: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    filter: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    abortSignal: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockReturnThis(),
    geojson: jest.fn().mockReturnThis(),
    explain: jest.fn().mockReturnThis(),
    rollback: jest.fn().mockReturnThis(),
    returns: jest.fn().mockReturnThis(),
  };

  // Configure default resolved values for operations
  queryBuilder.insert.mockResolvedValue({ data: null, error: null });
  queryBuilder.update.mockResolvedValue({ data: null, error: null });
  queryBuilder.delete.mockResolvedValue({ data: null, error: null });

  return queryBuilder;
}

export function createMockSupabaseClient(): MockSupabaseClient {
  return {
    auth: {
      signInWithOtp: jest.fn().mockResolvedValue({ data: null, error: null }),
      verifyOtp: jest.fn().mockResolvedValue({ data: null, error: null }),
      signInWithPassword: jest.fn().mockResolvedValue({ data: null, error: null }),
      signInWithOAuth: jest.fn().mockResolvedValue({ data: null, error: null }),
      signOut: jest.fn().mockResolvedValue({ error: null }),
      getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
      getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
      onAuthStateChange: jest.fn(() => ({
        data: { subscription: { unsubscribe: jest.fn() } }
      })),
    },
    from: jest.fn(() => createMockQueryBuilder()),
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn().mockResolvedValue({ data: null, error: null }),
        download: jest.fn().mockResolvedValue({ data: null, error: null }),
        list: jest.fn().mockResolvedValue({ data: [], error: null }),
        remove: jest.fn().mockResolvedValue({ data: null, error: null }),
        createSignedUrl: jest.fn().mockResolvedValue({ data: null, error: null }),
        createSignedUrls: jest.fn().mockResolvedValue({ data: [], error: null }),
        getPublicUrl: jest.fn().mockReturnValue({ data: { publicUrl: 'http://example.com/image.png' } }),
      })),
    },
    realtime: {
      channel: jest.fn(() => ({
        on: jest.fn().mockReturnThis(),
        subscribe: jest.fn(),
        unsubscribe: jest.fn(),
      })),
    },
    rpc: jest.fn().mockResolvedValue({ data: null, error: null }),
  };
}

// Global mock instance that can be imported and configured
export const mockSupabaseClient = createMockSupabaseClient();

// Helper functions for common test scenarios
export const mockHelpers = {
  // Mock successful authentication
  mockAuthenticatedUser: (user = { id: 'test-user-id', email: '<EMAIL>' }) => {
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user }, error: null });
    return user;
  },

  // Mock unauthenticated state
  mockUnauthenticatedUser: () => {
    mockSupabaseClient.auth.getUser.mockResolvedValue({ data: { user: null }, error: null });
  },

  // Mock successful data fetch
  mockSuccessfulQuery: (data: any, count?: number) => {
    const queryBuilder = createMockQueryBuilder();
    queryBuilder.single.mockResolvedValue({ data, error: null });
    queryBuilder.maybeSingle.mockResolvedValue({ data, error: null });
    if (count !== undefined) {
      queryBuilder.range.mockResolvedValue({ data: Array.isArray(data) ? data : [data], error: null, count });
    }
    mockSupabaseClient.from.mockReturnValue(queryBuilder);
    return queryBuilder;
  },

  // Mock query error
  mockQueryError: (error: any) => {
    const queryBuilder = createMockQueryBuilder();
    queryBuilder.single.mockResolvedValue({ data: null, error });
    queryBuilder.maybeSingle.mockResolvedValue({ data: null, error });
    queryBuilder.insert.mockResolvedValue({ data: null, error });
    queryBuilder.update.mockResolvedValue({ data: null, error });
    queryBuilder.delete.mockResolvedValue({ data: null, error });
    mockSupabaseClient.from.mockReturnValue(queryBuilder);
    return queryBuilder;
  },

  // Reset all mocks to default state
  resetMocks: () => {
    jest.clearAllMocks();
    Object.assign(mockSupabaseClient, createMockSupabaseClient());
  },
};

// Export the createClient function for direct mocking
export const createClient = jest.fn(() => mockSupabaseClient);

// Mock for @supabase/supabase-js
// Use the centralized Supabase mock factory
const { createClient: mockCreateClient, mockSupabaseClient } = require('./supabase/mockFactory');

export const createClient = mockCreateClient;
  storage: {
    from: jest.fn(() => ({
      upload: jest.fn(),
      download: jest.fn(),
      list: jest.fn(),
      remove: jest.fn(),
      createSignedUrl: jest.fn(),
      createSignedUrls: jest.fn(),
      getPublicUrl: jest.fn(),
    })),
  },
  realtime: {
    channel: jest.fn(() => ({
      on: jest.fn().mockReturnThis(),
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
    })),
  },
  rpc: jest.fn(),
}));

export default createClient;
